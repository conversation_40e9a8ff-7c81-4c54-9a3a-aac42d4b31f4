// Import plugin system
import { PluginFileProcessorService } from './fileProcessors/PluginFileProcessor'
import { ProcessedFileContent as PluginProcessedFileContent } from './fileProcessors/types'

export interface ProcessedFileContent {
  text?: string
  metadata?: any
  error?: string
}

export class FileProcessorService {
  private pluginProcessor: PluginFileProcessorService

  constructor() {
    this.pluginProcessor = new PluginFileProcessorService({
      fallbackEnabled: true,
      maxRetries: 2,
      timeout: 30000
    })
    console.log('🔧 FileProcessorService created (plugin-only)')
    // Initialize plugin system asynchronously
    this.initializeAsync()
  }

  private async initializeAsync(): Promise<void> {
    try {
      console.log('🔧 Initializing FileProcessorService plugin system...')
      await this.pluginProcessor.initialize()
      console.log('🔧 FileProcessorService plugin system initialized successfully')
    } catch (error) {
      console.warn('Failed to initialize plugin system:', error)
    }
  }

  // Ensure plugin system is initialized before use
  private async ensureInitialized(): Promise<void> {
    if (!this.pluginProcessor.isInitialized()) {
      await this.initializeAsync()
    }
  }

  // Main processing method using plugin system
  async processFile(filePath: string, fileType: string): Promise<ProcessedFileContent> {
    await this.ensureInitialized()
    const result = await this.pluginProcessor.processFile(filePath, fileType)
    return result as ProcessedFileContent
  }

  // Check if file type is supported
  async isFileTypeSupported(fileType: string): Promise<boolean> {
    await this.ensureInitialized()
    return await this.pluginProcessor.isFileTypeSupported(fileType)
  }

  // Synchronous version for backward compatibility
  isFileTypeSupportedSync(fileType: string): boolean {
    return this.pluginProcessor.isFileTypeSupportedSync(fileType)
  }

  // Get plugin information
  getPluginInfo() {
    return this.pluginProcessor.getPluginInfo()
  }

  // Cleanup
  async cleanup(): Promise<void> {
    if (this.pluginProcessor) {
      await this.pluginProcessor.cleanup()
    }
  }












  // Batch process multiple files
  async processFiles(files: Array<{ filePath: string, fileType: string }>): Promise<Array<ProcessedFileContent & { filePath: string }>> {
    return this.pluginProcessor.processFiles(files)
  }

  // Get supported file extensions
  getSupportedExtensions(): string[] {
    return this.pluginProcessor.getSupportedExtensions()
  }


}
